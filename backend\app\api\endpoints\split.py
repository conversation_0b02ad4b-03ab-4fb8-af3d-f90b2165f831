from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.crud import conversion as crud_conversion
from app.schemas.conversion import Conversion, ConversionCreate, SplitRequest
from app.models.conversion import ConversionStatus, TaskType
from app.services.pdf_split_service import pdf_split_service
from app.utils.validation import validate_split_mode, validate_filename, sanitize_filename
from app.utils.file_utils import ensure_directory_exists, clean_filename
from app.core.config import settings
import os
import uuid
from pathlib import Path

router = APIRouter()


@router.post("/", response_model=Conversion)
async def split_pdf(
    file: UploadFile = File(..., description="PDF file to split"),
    split_mode: str = Form("ranges", description="Split mode: ranges, fixed_range, remove_pages, filesize"),
    ranges: Optional[str] = Form(None, description="Page ranges (e.g., '1,5,10-14')"),
    fixed_range: Optional[int] = Form(None, description="Fixed range size"),
    remove_pages: Optional[str] = Form(None, description="Pages to remove (e.g., '1,4,8-12,16')"),
    merge_after: bool = Form(False, description="Merge all ranges after splitting"),
    db: Session = Depends(get_db)
):
    """
    Split a PDF file based on specified parameters
    
    - **file**: PDF file to split
    - **split_mode**: Split mode (ranges, fixed_range, remove_pages, filesize)
    - **ranges**: Page ranges for 'ranges' mode (e.g., "1,5,10-14")
    - **fixed_range**: Fixed range size for 'fixed_range' mode
    - **remove_pages**: Pages to remove for 'remove_pages' mode
    - **merge_after**: Whether to merge all ranges after splitting
    
    Returns the conversion record with split operation details.
    """
    try:
        # Validate file
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File must have a valid filename"
            )
        
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File must be a PDF"
            )
        
        # Validate split mode
        if not validate_split_mode(split_mode):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid split mode: {split_mode}"
            )
        
        # Validate mode-specific parameters
        if split_mode == "ranges" and not ranges:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Ranges parameter is required for 'ranges' split mode"
            )
        
        if split_mode == "fixed_range" and not fixed_range:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Fixed_range parameter is required for 'fixed_range' split mode"
            )
        
        if split_mode == "remove_pages" and not remove_pages:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Remove_pages parameter is required for 'remove_pages' split mode"
            )
        
        if split_mode == "fixed_range" and fixed_range and fixed_range < 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Fixed_range must be at least 1"
            )
        
        # Ensure upload directory exists
        upload_dir = Path(settings.UPLOAD_DIR)
        await ensure_directory_exists(upload_dir)
        
        # Check file size
        content = await file.read()
        file_size = len(content)
        
        if file_size > 100 * 1024 * 1024:  # 100MB
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail="File exceeds 100MB limit"
            )
        
        # Generate unique filename and save file
        clean_name = await clean_filename(file.filename)
        unique_filename = f"{uuid.uuid4()}_{clean_name}"
        file_path = upload_dir / unique_filename
        
        try:
            # Save file
            with open(file_path, 'wb') as f:
                f.write(content)
            
            # Create conversion record
            conversion_data = ConversionCreate(
                input_filename=file.filename,
                input_format="pdf",
                output_format="pdf",
                task_type=TaskType.SPLIT,
                file_size=file_size
            )
            
            conversion = crud_conversion.create(db=db, obj_in=conversion_data)
            
            try:
                # Update status to processing
                crud_conversion.update_status(
                    db=db, 
                    db_obj=conversion, 
                    status=ConversionStatus.PROCESSING
                )
                
                # Perform split operation
                result = await pdf_split_service.split_pdf(
                    file_path=unique_filename,
                    split_mode=split_mode,
                    ranges=ranges,
                    fixed_range=fixed_range,
                    remove_pages=remove_pages,
                    merge_after=merge_after
                )
                
                # Update conversion record with results
                output_files = result.get("output_files", [])
                output_filename = output_files[0] if output_files else "split_output.pdf"
                
                update_data = {
                    "status": ConversionStatus.COMPLETED,
                    "output_filename": output_filename,
                    "file_size": result.get("total_output_size", 0)
                }
                
                conversion = crud_conversion.update(
                    db=db, 
                    db_obj=conversion, 
                    obj_in=update_data
                )
                
                return conversion
                
            except Exception as e:
                # Update status to failed
                crud_conversion.update_status(
                    db=db, 
                    db_obj=conversion, 
                    status=ConversionStatus.FAILED,
                    error_message=str(e)
                )
                
                # Clean up input file
                try:
                    file_path.unlink(missing_ok=True)
                except:
                    pass
                
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Split operation failed: {str(e)}"
                )
        
        except Exception as e:
            # Clean up input file
            try:
                file_path.unlink(missing_ok=True)
            except:
                pass
            raise e
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error during split operation: {str(e)}"
        )


@router.post("/by-request", response_model=Conversion)
async def split_pdf_by_request(
    request: SplitRequest,
    db: Session = Depends(get_db)
):
    """
    Split a PDF file using a request object
    
    - **request**: Split request with file path and parameters
    
    Returns the conversion record with split operation details.
    """
    try:
        # Validate file exists
        upload_dir = Path(settings.UPLOAD_DIR)
        file_path = upload_dir / request.file
        
        if not file_path.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File not found: {request.file}"
            )
        
        # Get file size
        file_size = file_path.stat().st_size
        
        # Create conversion record
        conversion_data = ConversionCreate(
            input_filename=request.file,
            input_format="pdf",
            output_format="pdf",
            task_type=TaskType.SPLIT,
            file_size=file_size
        )
        
        conversion = crud_conversion.create(db=db, obj_in=conversion_data)
        
        try:
            # Update status to processing
            crud_conversion.update_status(
                db=db, 
                db_obj=conversion, 
                status=ConversionStatus.PROCESSING
            )
            
            # Perform split operation
            result = await pdf_split_service.split_pdf(
                file_path=request.file,
                split_mode=request.split_mode,
                ranges=request.ranges,
                fixed_range=request.fixed_range,
                remove_pages=request.remove_pages,
                merge_after=request.merge_after
            )
            
            # Update conversion record with results
            output_files = result.get("output_files", [])
            output_filename = output_files[0] if output_files else "split_output.pdf"
            
            update_data = {
                "status": ConversionStatus.COMPLETED,
                "output_filename": output_filename,
                "file_size": result.get("total_output_size", 0)
            }
            
            conversion = crud_conversion.update(
                db=db, 
                db_obj=conversion, 
                obj_in=update_data
            )
            
            return conversion
            
        except Exception as e:
            # Update status to failed
            crud_conversion.update_status(
                db=db, 
                db_obj=conversion, 
                status=ConversionStatus.FAILED,
                error_message=str(e)
            )
            
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Split operation failed: {str(e)}"
            )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error during split operation: {str(e)}"
        )
