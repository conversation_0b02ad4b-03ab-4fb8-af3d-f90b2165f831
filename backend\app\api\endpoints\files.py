from typing import List
from fastapi import APIRouter, Depends, UploadFile, File, HTTPException
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.crud import file as crud_file
from app.schemas.file import File as FileSchema, FileCreate

router = APIRouter()

@router.post("/upload", response_model=FileSchema)
async def upload_file(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """Upload a file and create database record"""
    # Calculate file size
    file_size = 0
    if hasattr(file, 'size') and file.size:
        file_size = file.size

    # Create file record using CRUD
    file_create = FileCreate(
        filename=file.filename,
        original_filename=file.filename,
        file_path=f"uploads/{file.filename}",
        file_size=file_size,
        content_type=file.content_type or "application/octet-stream"
    )

    return crud_file.create(db=db, obj_in=file_create)

@router.get("/", response_model=List[FileSchema])
def get_files(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """Get all files with pagination"""
    return crud_file.get_multi(db=db, skip=skip, limit=limit)

@router.get("/{file_id}", response_model=FileSchema)
def get_file(
    file_id: int,
    db: Session = Depends(get_db)
):
    """Get a specific file by ID"""
    file_record = crud_file.get(db=db, id=file_id)
    if not file_record:
        raise HTTPException(status_code=404, detail="File not found")
    return file_record

@router.get("/by-filename/{filename}", response_model=FileSchema)
def get_file_by_filename(
    filename: str,
    db: Session = Depends(get_db)
):
    """Get a file by filename"""
    file_record = crud_file.get_by_filename(db=db, filename=filename)
    if not file_record:
        raise HTTPException(status_code=404, detail="File not found")
    return file_record

@router.delete("/{file_id}", response_model=FileSchema)
def delete_file(
    file_id: int,
    db: Session = Depends(get_db)
):
    """Delete a file by ID"""
    file_record = crud_file.get(db=db, id=file_id)
    if not file_record:
        raise HTTPException(status_code=404, detail="File not found")
    return crud_file.remove(db=db, id=file_id)
