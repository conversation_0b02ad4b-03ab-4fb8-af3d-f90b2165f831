from typing import List, Optional
from sqlalchemy.orm import Session
from app.crud.base import CRUDBase
from app.models.file import FileRecord
from app.schemas.file import FileCreate, FileUpdate


class CRUDFile(CRUDBase[FileRecord, FileCreate, FileUpdate]):
    def get_by_filename(self, db: Session, *, filename: str) -> Optional[FileRecord]:
        """Get a file record by filename"""
        return db.query(FileRecord).filter(FileRecord.filename == filename).first()

    def get_by_original_filename(self, db: Session, *, original_filename: str) -> List[FileRecord]:
        """Get file records by original filename"""
        return db.query(FileRecord).filter(FileRecord.original_filename == original_filename).all()

    def get_by_content_type(self, db: Session, *, content_type: str) -> List[FileRecord]:
        """Get file records by content type"""
        return db.query(FileRecord).filter(FileRecord.content_type == content_type).all()

    def get_files_by_size_range(
        self, db: Session, *, min_size: int = 0, max_size: int = None
    ) -> List[FileRecord]:
        """Get file records within a size range"""
        query = db.query(FileRecord).filter(FileRecord.file_size >= min_size)
        if max_size is not None:
            query = query.filter(FileRecord.file_size <= max_size)
        return query.all()

    def get_recent_files(self, db: Session, *, limit: int = 10) -> List[FileRecord]:
        """Get most recently created files"""
        return (
            db.query(FileRecord)
            .order_by(FileRecord.created_at.desc())
            .limit(limit)
            .all()
        )


file = CRUDFile(FileRecord)
