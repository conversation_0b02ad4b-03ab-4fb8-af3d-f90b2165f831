import os
import async<PERSON>
from typing import Dict, Any
from pathlib import Path
import <PERSON>yPD<PERSON>2
from PyPDF2 import Pd<PERSON><PERSON><PERSON>er
from docx import Document
from docx.shared import Inches
from app.core.config import settings
from app.utils.file_utils import validate_pdf_file, get_file_size
from app.utils.validation import validate_file_size, validate_output_format


class PDFToWordService:
    """Service for converting PDF files to Word documents"""
    
    def __init__(self):
        self.max_file_size = 100 * 1024 * 1024  # 100MB
        self.upload_dir = Path(settings.UPLOAD_DIR)
        self.supported_formats = ["docx", "doc"]
    
    async def convert_pdf_to_word(
        self, 
        file_path: str,
        output_format: str = "docx"
    ) -> Dict[str, Any]:
        """
        Convert a PDF file to Word document
        
        Args:
            file_path: Path to the PDF file to convert
            output_format: Output format (docx, doc)
            
        Returns:
            dict: Result containing output file path and conversion statistics
            
        Raises:
            ValueError: If validation fails
            FileNotFoundError: If input file doesn't exist
            Exception: If conversion operation fails
        """
        try:
            # Validate inputs
            await self._validate_conversion_inputs(file_path, output_format)
            
            full_path = self.upload_dir / file_path
            
            # Get original file info
            original_size = await get_file_size(str(full_path))
            
            # Create output filename
            output_filename = f"converted_{Path(file_path).stem}.{output_format}"
            output_path = self.upload_dir / output_filename
            
            # Perform conversion operation
            result = await self._convert_pdf_file(full_path, output_path, output_format)
            
            # Get converted file info
            converted_size = await get_file_size(str(output_path))
            
            return {
                "success": True,
                "input_file": file_path,
                "output_file": output_filename,
                "output_format": output_format,
                "original_size": original_size,
                "converted_size": converted_size,
                **result
            }
            
        except Exception as e:
            # Clean up output file if it exists
            if 'output_path' in locals() and output_path.exists():
                output_path.unlink()
            raise e
    
    async def _validate_conversion_inputs(self, file_path: str, output_format: str) -> None:
        """Validate conversion operation inputs"""
        
        full_path = self.upload_dir / file_path
        
        # Check if file exists
        if not full_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        # Validate PDF format
        if not await validate_pdf_file(str(full_path)):
            raise ValueError(f"Invalid PDF file: {file_path}")
        
        # Check file size
        file_size = await get_file_size(str(full_path))
        if not validate_file_size(file_size, self.max_file_size):
            raise ValueError(f"File too large: {file_path} ({file_size} bytes)")
        
        # Validate output format
        if output_format.lower() not in self.supported_formats:
            raise ValueError(f"Unsupported output format: {output_format}. Supported formats: {', '.join(self.supported_formats)}")
    
    async def _convert_pdf_file(
        self, 
        input_path: Path, 
        output_path: Path, 
        output_format: str
    ) -> Dict[str, Any]:
        """Perform the actual PDF to Word conversion operation"""
        
        def convert_operation():
            # Create a new Word document
            doc = Document()
            
            # Set document margins
            sections = doc.sections
            for section in sections:
                section.top_margin = Inches(1)
                section.bottom_margin = Inches(1)
                section.left_margin = Inches(1)
                section.right_margin = Inches(1)
            
            # Read PDF content
            with open(input_path, 'rb') as file:
                reader = PdfReader(file)
                
                total_pages = len(reader.pages)
                extracted_text = ""
                
                # Extract text from each page
                for page_num, page in enumerate(reader.pages):
                    try:
                        page_text = page.extract_text()
                        
                        if page_text.strip():
                            # Add page header
                            if page_num > 0:
                                doc.add_page_break()
                            
                            # Add page number as heading
                            heading = doc.add_heading(f'Page {page_num + 1}', level=2)
                            
                            # Add extracted text as paragraphs
                            # Split text into paragraphs (by double newlines or long single newlines)
                            paragraphs = page_text.split('\n\n')
                            if len(paragraphs) == 1:
                                # If no double newlines, split by single newlines but group short lines
                                lines = page_text.split('\n')
                                paragraphs = []
                                current_paragraph = ""
                                
                                for line in lines:
                                    line = line.strip()
                                    if line:
                                        if len(line) < 50 and current_paragraph:
                                            # Short line, likely a continuation
                                            current_paragraph += " " + line
                                        else:
                                            if current_paragraph:
                                                paragraphs.append(current_paragraph)
                                            current_paragraph = line
                                    else:
                                        if current_paragraph:
                                            paragraphs.append(current_paragraph)
                                            current_paragraph = ""
                                
                                if current_paragraph:
                                    paragraphs.append(current_paragraph)
                            
                            # Add paragraphs to document
                            for paragraph_text in paragraphs:
                                paragraph_text = paragraph_text.strip()
                                if paragraph_text:
                                    doc.add_paragraph(paragraph_text)
                            
                            extracted_text += page_text + "\n\n"
                    
                    except Exception as e:
                        # Add error note for problematic pages
                        doc.add_paragraph(f"[Error extracting text from page {page_num + 1}: {str(e)}]")
                
                # Add document metadata
                doc.core_properties.title = f"Converted from {input_path.name}"
                doc.core_properties.author = "Any2PDF Converter"
                doc.core_properties.subject = "PDF to Word Conversion"
                
                # Save the document
                doc.save(str(output_path))
                
                return {
                    "pages_processed": total_pages,
                    "text_extracted": bool(extracted_text.strip()),
                    "output_format": output_format,
                    "paragraphs_created": len([p for p in doc.paragraphs if p.text.strip()])
                }
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, convert_operation)
    
    async def get_pdf_text_preview(self, file_path: str, max_pages: int = 3) -> Dict[str, Any]:
        """Get a preview of text that would be extracted from PDF"""
        
        def extract_preview():
            full_path = self.upload_dir / file_path
            
            with open(full_path, 'rb') as file:
                reader = PdfReader(file)
                
                total_pages = len(reader.pages)
                preview_pages = min(max_pages, total_pages)
                preview_text = ""
                
                for page_num in range(preview_pages):
                    try:
                        page = reader.pages[page_num]
                        page_text = page.extract_text()
                        
                        if page_text.strip():
                            preview_text += f"=== Page {page_num + 1} ===\n"
                            preview_text += page_text[:500]  # First 500 characters
                            if len(page_text) > 500:
                                preview_text += "...\n\n"
                            else:
                                preview_text += "\n\n"
                    
                    except Exception:
                        preview_text += f"=== Page {page_num + 1} ===\n[Error extracting text]\n\n"
                
                return {
                    "total_pages": total_pages,
                    "preview_pages": preview_pages,
                    "preview_text": preview_text,
                    "has_extractable_text": bool(preview_text.strip())
                }
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, extract_preview)
    
    async def get_conversion_info(self, file_path: str) -> Dict[str, Any]:
        """Get information about PDF conversion capabilities"""
        
        def get_info():
            full_path = self.upload_dir / file_path
            
            with open(full_path, 'rb') as file:
                reader = PdfReader(file)
                
                total_pages = len(reader.pages)
                has_text = False
                text_pages = 0
                
                # Check first few pages for text content
                check_pages = min(5, total_pages)
                
                for page_num in range(check_pages):
                    try:
                        page = reader.pages[page_num]
                        page_text = page.extract_text()
                        
                        if page_text.strip():
                            has_text = True
                            text_pages += 1
                    
                    except Exception:
                        continue
                
                return {
                    "total_pages": total_pages,
                    "has_extractable_text": has_text,
                    "text_pages_sample": text_pages,
                    "estimated_text_coverage": (text_pages / check_pages) * 100 if check_pages > 0 else 0,
                    "recommended_format": "docx",
                    "supported_formats": self.supported_formats
                }
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, get_info)


# Global service instance
pdf_to_word_service = PDFToWordService()
