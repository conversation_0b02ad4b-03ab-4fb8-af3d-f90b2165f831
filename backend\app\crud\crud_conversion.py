from typing import List, Optional
from sqlalchemy.orm import Session
from app.crud.base import CRUDBase
from app.models.conversion import ConversionRecord, ConversionStatus
from app.schemas.conversion import ConversionCreate, ConversionUpdate


class CRUDConversion(CRUDBase[ConversionRecord, ConversionCreate, ConversionUpdate]):
    def get_by_status(self, db: Session, *, status: ConversionStatus) -> List[ConversionRecord]:
        """Get conversion records by status"""
        return db.query(ConversionRecord).filter(ConversionRecord.status == status).all()

    def get_by_input_filename(self, db: Session, *, input_filename: str) -> List[ConversionRecord]:
        """Get conversion records by input filename"""
        return db.query(ConversionRecord).filter(ConversionRecord.input_filename == input_filename).all()

    def get_by_format_conversion(
        self, db: Session, *, input_format: str, output_format: str
    ) -> List[ConversionRecord]:
        """Get conversion records by format conversion type"""
        return (
            db.query(ConversionRecord)
            .filter(
                ConversionRecord.input_format == input_format,
                ConversionRecord.output_format == output_format
            )
            .all()
        )

    def get_pending_conversions(self, db: Session) -> List[ConversionRecord]:
        """Get all pending conversion records"""
        return self.get_by_status(db, status=ConversionStatus.PENDING)

    def get_processing_conversions(self, db: Session) -> List[ConversionRecord]:
        """Get all processing conversion records"""
        return self.get_by_status(db, status=ConversionStatus.PROCESSING)

    def get_completed_conversions(self, db: Session, *, limit: int = 100) -> List[ConversionRecord]:
        """Get completed conversion records with limit"""
        return (
            db.query(ConversionRecord)
            .filter(ConversionRecord.status == ConversionStatus.COMPLETED)
            .order_by(ConversionRecord.completed_at.desc())
            .limit(limit)
            .all()
        )

    def get_failed_conversions(self, db: Session, *, limit: int = 100) -> List[ConversionRecord]:
        """Get failed conversion records with limit"""
        return (
            db.query(ConversionRecord)
            .filter(ConversionRecord.status == ConversionStatus.FAILED)
            .order_by(ConversionRecord.created_at.desc())
            .limit(limit)
            .all()
        )

    def get_recent_conversions(self, db: Session, *, limit: int = 10) -> List[ConversionRecord]:
        """Get most recently created conversion records"""
        return (
            db.query(ConversionRecord)
            .order_by(ConversionRecord.created_at.desc())
            .limit(limit)
            .all()
        )

    def update_status(
        self, db: Session, *, db_obj: ConversionRecord, status: ConversionStatus, error_message: Optional[str] = None
    ) -> ConversionRecord:
        """Update conversion status and optionally set error message"""
        update_data = {"status": status}
        if error_message:
            update_data["error_message"] = error_message
        if status == ConversionStatus.COMPLETED:
            from datetime import datetime
            update_data["completed_at"] = datetime.utcnow()
        
        return self.update(db, db_obj=db_obj, obj_in=update_data)


conversion = CRUDConversion(ConversionRecord)
