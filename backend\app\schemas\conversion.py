from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field
from app.models.conversion import ConversionStatus, TaskType


# Shared properties
class ConversionBase(BaseModel):
    input_filename: str
    input_format: str
    output_format: str
    task_type: TaskType
    output_filename: Optional[str] = None
    file_size: Optional[int] = None
    input_file_id: Optional[int] = None
    output_file_id: Optional[int] = None


# Properties to receive on conversion creation
class ConversionCreate(ConversionBase):
    pass


# Properties to receive on conversion update
class ConversionUpdate(BaseModel):
    output_filename: Optional[str] = None
    status: Optional[ConversionStatus] = None
    completed_at: Optional[datetime] = None
    file_size: Optional[int] = None
    error_message: Optional[str] = None
    output_file_id: Optional[int] = None


# Properties shared by models stored in DB
class ConversionInDBBase(ConversionBase):
    id: int
    status: ConversionStatus
    created_at: datetime
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None

    class Config:
        from_attributes = True


# Properties to return to client
class Conversion(ConversionInDBBase):
    pass


# Properties stored in DB
class ConversionInDB(ConversionInDBBase):
    pass


# Specific schemas for PDF operations
class MergeRequest(BaseModel):
    """Request schema for PDF merge operation"""
    files: List[str] = Field(..., min_items=2, max_items=50, description="List of file paths to merge (2-50 files)")
    output_filename: Optional[str] = Field(None, description="Custom output filename")

    class Config:
        json_schema_extra = {
            "example": {
                "files": ["file1.pdf", "file2.pdf", "file3.pdf"],
                "output_filename": "merged_document.pdf"
            }
        }


class SplitRequest(BaseModel):
    """Request schema for PDF split operation"""
    file: str = Field(..., description="File path to split")
    split_mode: str = Field("ranges", description="Split mode: ranges, fixed_range, remove_pages, filesize")
    ranges: Optional[str] = Field(None, description="Page ranges (e.g., '1,5,10-14')")
    fixed_range: Optional[int] = Field(None, description="Fixed range size")
    remove_pages: Optional[str] = Field(None, description="Pages to remove (e.g., '1,4,8-12,16')")
    merge_after: bool = Field(False, description="Merge all ranges after splitting")

    class Config:
        json_schema_extra = {
            "example": {
                "file": "document.pdf",
                "split_mode": "ranges",
                "ranges": "1-5,10-15,20-25"
            }
        }


class CompressRequest(BaseModel):
    """Request schema for PDF compression operation"""
    file: str = Field(..., description="File path to compress")
    compression_level: str = Field("recommended", description="Compression level: extreme, recommended, low")

    class Config:
        json_schema_extra = {
            "example": {
                "file": "document.pdf",
                "compression_level": "recommended"
            }
        }


class PdfToWordRequest(BaseModel):
    """Request schema for PDF to Word conversion"""
    file: str = Field(..., description="PDF file path to convert")
    output_format: str = Field("docx", description="Output format: docx, doc")

    class Config:
        json_schema_extra = {
            "example": {
                "file": "document.pdf",
                "output_format": "docx"
            }
        }


class ImageToPdfRequest(BaseModel):
    """Request schema for Image to PDF conversion"""
    files: List[str] = Field(..., min_items=1, max_items=50, description="List of image file paths")
    orientation: str = Field("portrait", description="Page orientation: portrait, landscape")
    margin: int = Field(0, ge=0, description="Margin in pixels")
    pagesize: str = Field("fit", description="Page size: fit, A4, letter")
    merge_after: bool = Field(True, description="Merge all images into single PDF")

    class Config:
        json_schema_extra = {
            "example": {
                "files": ["image1.jpg", "image2.png"],
                "orientation": "portrait",
                "pagesize": "A4",
                "merge_after": True
            }
        }