from datetime import datetime
from typing import Optional
from pydantic import BaseModel
from app.models.conversion import ConversionStatus


# Shared properties
class ConversionBase(BaseModel):
    input_filename: str
    input_format: str
    output_format: str
    output_filename: Optional[str] = None
    file_size: Optional[int] = None
    input_file_id: Optional[int] = None
    output_file_id: Optional[int] = None


# Properties to receive on conversion creation
class ConversionCreate(ConversionBase):
    pass


# Properties to receive on conversion update
class ConversionUpdate(BaseModel):
    output_filename: Optional[str] = None
    status: Optional[ConversionStatus] = None
    completed_at: Optional[datetime] = None
    file_size: Optional[int] = None
    error_message: Optional[str] = None
    output_file_id: Optional[int] = None


# Properties shared by models stored in DB
class ConversionInDBBase(ConversionBase):
    id: int
    status: ConversionStatus
    created_at: datetime
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None

    class Config:
        from_attributes = True


# Properties to return to client
class Conversion(ConversionInDBBase):
    pass


# Properties stored in DB
class ConversionInDB(ConversionInDBBase):
    pass