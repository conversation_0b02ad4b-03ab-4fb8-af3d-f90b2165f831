import os
import asyncio
import mimetypes
from pathlib import Path
from typing import Optional, Union
import PyPDF2
from PyPDF2 import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


async def validate_pdf_file(file_path: str) -> bool:
    """
    Validate if a file is a valid PDF

    Args:
        file_path: Path to the file to validate

    Returns:
        bool: True if valid PDF, False otherwise
    """
    try:
        def check_pdf():
            with open(file_path, 'rb') as file:
                reader = PdfReader(file)
                # Try to access pages to ensure it's a valid PDF
                return len(reader.pages) > 0

        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, check_pdf)
    except Exception:
        return False


async def get_file_size(file_path: str) -> int:
    """
    Get file size in bytes

    Args:
        file_path: Path to the file

    Returns:
        int: File size in bytes
    """
    try:
        return os.path.getsize(file_path)
    except OSError:
        return 0


async def get_mime_type(file_path: str) -> Optional[str]:
    """
    Get MIME type of a file

    Args:
        file_path: Path to the file

    Returns:
        str: MIME type or None if cannot be determined
    """
    mime_type, _ = mimetypes.guess_type(file_path)
    return mime_type


async def validate_image_file(file_path: str) -> bool:
    """
    Validate if a file is a valid image

    Args:
        file_path: Path to the file to validate

    Returns:
        bool: True if valid image, False otherwise
    """
    try:
        from PIL import Image

        def check_image():
            with Image.open(file_path) as img:
                img.verify()
                return True

        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, check_image)
    except Exception:
        return False


async def ensure_directory_exists(directory_path: Union[str, Path]) -> None:
    """
    Ensure a directory exists, create if it doesn't

    Args:
        directory_path: Path to the directory
    """
    path = Path(directory_path)
    path.mkdir(parents=True, exist_ok=True)


async def clean_filename(filename: str) -> str:
    """
    Clean filename by removing invalid characters

    Args:
        filename: Original filename

    Returns:
        str: Cleaned filename
    """
    # Remove invalid characters for file systems
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')

    # Limit length
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        filename = name[:255-len(ext)] + ext

    return filename


async def get_file_extension(file_path: str) -> str:
    """
    Get file extension from path

    Args:
        file_path: Path to the file

    Returns:
        str: File extension (including dot)
    """
    return Path(file_path).suffix.lower()


async def is_pdf_file(file_path: str) -> bool:
    """
    Check if file is a PDF based on extension and MIME type

    Args:
        file_path: Path to the file

    Returns:
        bool: True if PDF file
    """
    extension = await get_file_extension(file_path)
    mime_type = await get_mime_type(file_path)

    return extension == '.pdf' or mime_type == 'application/pdf'


async def is_image_file(file_path: str) -> bool:
    """
    Check if file is an image based on extension and MIME type

    Args:
        file_path: Path to the file

    Returns:
        bool: True if image file
    """
    extension = await get_file_extension(file_path)
    mime_type = await get_mime_type(file_path)

    image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
    image_mime_types = {'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/tiff', 'image/webp'}

    return extension in image_extensions or (mime_type and mime_type in image_mime_types)