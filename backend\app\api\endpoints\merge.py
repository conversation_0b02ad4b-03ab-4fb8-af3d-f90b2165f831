from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.crud import conversion as crud_conversion
from app.schemas.conversion import Conversion, ConversionCreate, MergeRequest
from app.models.conversion import ConversionStatus, TaskType
from app.services.pdf_merge_service import pdf_merge_service
from app.utils.validation import validate_file_count, validate_filename, sanitize_filename
from app.utils.file_utils import ensure_directory_exists, clean_filename
from app.core.config import settings
import os
import uuid
from pathlib import Path

router = APIRouter()


@router.post("/", response_model=Conversion)
async def merge_pdfs(
    files: List[UploadFile] = File(..., description="PDF files to merge (2-50 files)"),
    output_filename: str = Form(None, description="Custom output filename"),
    db: Session = Depends(get_db)
):
    """
    Merge multiple PDF files into a single PDF
    
    - **files**: List of PDF files to merge (minimum 2, maximum 50)
    - **output_filename**: Optional custom output filename
    
    Returns the conversion record with merge operation details.
    """
    try:
        # Validate file count
        if not validate_file_count(len(files), min_count=2, max_count=50):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Must provide between 2 and 50 PDF files for merging"
            )
        
        # Ensure upload directory exists
        upload_dir = Path(settings.UPLOAD_DIR)
        await ensure_directory_exists(upload_dir)
        
        # Save uploaded files and collect file paths
        saved_files = []
        total_size = 0
        
        try:
            for file in files:
                # Validate file
                if not file.filename:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="All files must have valid filenames"
                    )
                
                if not file.filename.lower().endswith('.pdf'):
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"File {file.filename} is not a PDF file"
                    )
                
                # Check file size
                content = await file.read()
                file_size = len(content)
                total_size += file_size
                
                if file_size > 100 * 1024 * 1024:  # 100MB
                    raise HTTPException(
                        status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                        detail=f"File {file.filename} exceeds 100MB limit"
                    )
                
                # Generate unique filename
                clean_name = await clean_filename(file.filename)
                unique_filename = f"{uuid.uuid4()}_{clean_name}"
                file_path = upload_dir / unique_filename
                
                # Save file
                with open(file_path, 'wb') as f:
                    f.write(content)
                
                saved_files.append(unique_filename)
                
                # Reset file position for potential re-reading
                await file.seek(0)
        
        except Exception as e:
            # Clean up any saved files on error
            for saved_file in saved_files:
                try:
                    (upload_dir / saved_file).unlink(missing_ok=True)
                except:
                    pass
            raise e
        
        # Check total size
        if total_size > 200 * 1024 * 1024:  # 200MB total
            # Clean up files
            for saved_file in saved_files:
                try:
                    (upload_dir / saved_file).unlink(missing_ok=True)
                except:
                    pass
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail="Total file size exceeds 200MB limit"
            )
        
        # Create conversion record
        conversion_data = ConversionCreate(
            input_filename=f"{len(saved_files)} PDF files",
            input_format="pdf",
            output_format="pdf",
            task_type=TaskType.MERGE,
            file_size=total_size
        )
        
        conversion = crud_conversion.create(db=db, obj_in=conversion_data)
        
        try:
            # Update status to processing
            crud_conversion.update_status(
                db=db, 
                db_obj=conversion, 
                status=ConversionStatus.PROCESSING
            )
            
            # Perform merge operation
            result = await pdf_merge_service.merge_pdfs(
                file_paths=saved_files,
                output_filename=output_filename
            )
            
            # Update conversion record with results
            update_data = {
                "status": ConversionStatus.COMPLETED,
                "output_filename": result["output_filename"],
                "file_size": result["file_size"]
            }
            
            conversion = crud_conversion.update(
                db=db, 
                db_obj=conversion, 
                obj_in=update_data
            )
            
            return conversion
            
        except Exception as e:
            # Update status to failed
            crud_conversion.update_status(
                db=db, 
                db_obj=conversion, 
                status=ConversionStatus.FAILED,
                error_message=str(e)
            )
            
            # Clean up files
            for saved_file in saved_files:
                try:
                    (upload_dir / saved_file).unlink(missing_ok=True)
                except:
                    pass
            
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Merge operation failed: {str(e)}"
            )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error during merge operation: {str(e)}"
        )


@router.get("/info/{file_path}")
async def get_pdf_info(file_path: str):
    """
    Get information about a PDF file
    
    - **file_path**: Path to the PDF file
    
    Returns PDF metadata including page count, title, author, etc.
    """
    try:
        info = await pdf_merge_service.get_pdf_info(file_path)
        return {
            "success": True,
            "file_path": file_path,
            "info": info
        }
    except FileNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"PDF file not found: {file_path}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error reading PDF info: {str(e)}"
        )
