# Any2PDF API Documentation

## Overview

The Any2PDF API is a RESTful service built with FastAPI that provides file conversion capabilities between various formats, starting with JPG to PDF and PDF to JPG conversions.

**Base URL**: `http://localhost:8000` (development)  
**API Version**: v1  
**Content-Type**: `application/json` (unless specified otherwise)

## Authentication

Currently, the API does not require authentication. Future versions will implement JWT-based authentication.

## Rate Limiting

- **Upload Rate**: 10 requests per minute per IP
- **Conversion Rate**: 5 requests per minute per IP
- **File Size Limit**: 50MB per file

## Common Response Format

All API responses follow this structure:

```json
{
  "success": true,
  "data": {},
  "message": "Operation completed successfully",
  "timestamp": "2025-01-06T10:30:00Z"
}
```

Error responses:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid file format",
    "details": {}
  },
  "timestamp": "2025-01-06T10:30:00Z"
}
```

## Endpoints

### Health Check

#### GET /health

Check API health status.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "timestamp": "2025-01-06T10:30:00Z"
  }
}
```

### File Upload

#### POST /api/v1/files/upload

Upload files for conversion.

**Request:**
- Content-Type: `multipart/form-data`
- Body: Form data with file(s)

**Parameters:**
- `files`: File(s) to upload (required)

**Response:**
```json
{
  "success": true,
  "data": {
    "files": [
      {
        "id": "uuid-here",
        "filename": "generated-filename.jpg",
        "original_name": "my-image.jpg",
        "size": 1024000,
        "mime_type": "image/jpeg",
        "uploaded_at": "2025-01-06T10:30:00Z"
      }
    ]
  }
}
```

### File Conversion

#### POST /api/v1/conversion/convert

Convert uploaded files to target format.

**Request:**
```json
{
  "file_ids": ["uuid1", "uuid2"],
  "target_format": "pdf",
  "options": {
    "quality": "high",
    "page_size": "A4",
    "orientation": "portrait"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "conversion_id": "uuid-here",
    "status": "pending",
    "estimated_time": 30
  }
}
```

#### GET /api/v1/conversion/{conversion_id}/status

Check conversion status.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid-here",
    "status": "completed",
    "progress": 100,
    "created_at": "2025-01-06T10:30:00Z",
    "completed_at": "2025-01-06T10:30:45Z",
    "download_url": "/api/v1/files/download/output-uuid"
  }
}
```

### File Download

#### GET /api/v1/files/download/{file_id}

Download converted file.

**Response:**
- Content-Type: Appropriate MIME type
- Content-Disposition: attachment; filename="converted-file.pdf"
- Binary file content

## Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `VALIDATION_ERROR` | 400 | Invalid request data |
| `FILE_TOO_LARGE` | 413 | File exceeds size limit |
| `UNSUPPORTED_FORMAT` | 400 | File format not supported |
| `CONVERSION_FAILED` | 500 | Conversion process failed |
| `FILE_NOT_FOUND` | 404 | Requested file not found |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests |

## Supported Formats

### Input Formats
- **Images**: JPG, JPEG, PNG, GIF, BMP, TIFF
- **Documents**: PDF

### Output Formats
- **Images**: JPG, PNG
- **Documents**: PDF

## Conversion Options

### PDF Generation Options
```json
{
  "page_size": "A4|A3|A5|Letter|Legal",
  "orientation": "portrait|landscape",
  "quality": "low|medium|high",
  "margin": {
    "top": 20,
    "right": 20,
    "bottom": 20,
    "left": 20
  }
}
```

### Image Extraction Options
```json
{
  "format": "jpg|png",
  "quality": 85,
  "dpi": 300,
  "pages": "all|1-5|1,3,5"
}
```

## WebSocket Events (Future)

Real-time conversion progress updates will be available via WebSocket connection.

**Connection**: `ws://localhost:8000/ws/conversion/{conversion_id}`

**Events:**
- `progress`: Conversion progress update
- `completed`: Conversion completed
- `error`: Conversion failed

---

**Last Updated**: January 2025  
**API Version**: 1.0.0
