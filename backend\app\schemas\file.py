from datetime import datetime
from typing import Optional
from pydantic import BaseModel


# Shared properties
class FileBase(BaseModel):
    filename: str
    original_filename: str
    file_path: str
    file_size: int
    content_type: str


# Properties to receive on file creation
class FileCreate(FileBase):
    pass


# Properties to receive on file update
class FileUpdate(BaseModel):
    filename: Optional[str] = None
    original_filename: Optional[str] = None
    file_path: Optional[str] = None
    file_size: Optional[int] = None
    content_type: Optional[str] = None


# Properties shared by models stored in DB
class FileInDBBase(FileBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True


# Properties to return to client
class File(FileInDBBase):
    pass


# Properties stored in DB
class FileInDB(FileInDBBase):
    pass
