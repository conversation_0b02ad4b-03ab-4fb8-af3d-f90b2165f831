from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.crud import conversion as crud_conversion
from app.schemas.conversion import Conversion, ConversionCreate, CompressRequest
from app.models.conversion import ConversionStatus, TaskType
from app.services.pdf_compress_service import pdf_compress_service
from app.utils.validation import validate_compression_level, validate_filename, sanitize_filename
from app.utils.file_utils import ensure_directory_exists, clean_filename
from app.core.config import settings
import os
import uuid
from pathlib import Path

router = APIRouter()


@router.post("/", response_model=Conversion)
async def compress_pdf(
    file: UploadFile = File(..., description="PDF file to compress"),
    compression_level: str = Form("recommended", description="Compression level: extreme, recommended, low"),
    db: Session = Depends(get_db)
):
    """
    Compress a PDF file with specified compression level
    
    - **file**: PDF file to compress
    - **compression_level**: Compression level (extreme, recommended, low)
    
    Returns the conversion record with compression operation details.
    """
    try:
        # Validate file
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File must have a valid filename"
            )
        
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File must be a PDF"
            )
        
        # Validate compression level
        if not validate_compression_level(compression_level):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid compression level: {compression_level}. Must be one of: extreme, recommended, low"
            )
        
        # Ensure upload directory exists
        upload_dir = Path(settings.UPLOAD_DIR)
        await ensure_directory_exists(upload_dir)
        
        # Check file size
        content = await file.read()
        file_size = len(content)
        
        if file_size > 100 * 1024 * 1024:  # 100MB
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail="File exceeds 100MB limit"
            )
        
        # Generate unique filename and save file
        clean_name = await clean_filename(file.filename)
        unique_filename = f"{uuid.uuid4()}_{clean_name}"
        file_path = upload_dir / unique_filename
        
        try:
            # Save file
            with open(file_path, 'wb') as f:
                f.write(content)
            
            # Create conversion record
            conversion_data = ConversionCreate(
                input_filename=file.filename,
                input_format="pdf",
                output_format="pdf",
                task_type=TaskType.COMPRESS,
                file_size=file_size
            )
            
            conversion = crud_conversion.create(db=db, obj_in=conversion_data)
            
            try:
                # Update status to processing
                crud_conversion.update_status(
                    db=db, 
                    db_obj=conversion, 
                    status=ConversionStatus.PROCESSING
                )
                
                # Perform compression operation
                result = await pdf_compress_service.compress_pdf(
                    file_path=unique_filename,
                    compression_level=compression_level
                )
                
                # Update conversion record with results
                update_data = {
                    "status": ConversionStatus.COMPLETED,
                    "output_filename": result["output_file"],
                    "file_size": result["compressed_size"]
                }
                
                conversion = crud_conversion.update(
                    db=db, 
                    db_obj=conversion, 
                    obj_in=update_data
                )
                
                return conversion
                
            except Exception as e:
                # Update status to failed
                crud_conversion.update_status(
                    db=db, 
                    db_obj=conversion, 
                    status=ConversionStatus.FAILED,
                    error_message=str(e)
                )
                
                # Clean up input file
                try:
                    file_path.unlink(missing_ok=True)
                except:
                    pass
                
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Compression operation failed: {str(e)}"
                )
        
        except Exception as e:
            # Clean up input file
            try:
                file_path.unlink(missing_ok=True)
            except:
                pass
            raise e
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error during compression operation: {str(e)}"
        )


@router.post("/by-request", response_model=Conversion)
async def compress_pdf_by_request(
    request: CompressRequest,
    db: Session = Depends(get_db)
):
    """
    Compress a PDF file using a request object
    
    - **request**: Compression request with file path and compression level
    
    Returns the conversion record with compression operation details.
    """
    try:
        # Validate file exists
        upload_dir = Path(settings.UPLOAD_DIR)
        file_path = upload_dir / request.file
        
        if not file_path.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File not found: {request.file}"
            )
        
        # Get file size
        file_size = file_path.stat().st_size
        
        # Create conversion record
        conversion_data = ConversionCreate(
            input_filename=request.file,
            input_format="pdf",
            output_format="pdf",
            task_type=TaskType.COMPRESS,
            file_size=file_size
        )
        
        conversion = crud_conversion.create(db=db, obj_in=conversion_data)
        
        try:
            # Update status to processing
            crud_conversion.update_status(
                db=db, 
                db_obj=conversion, 
                status=ConversionStatus.PROCESSING
            )
            
            # Perform compression operation
            result = await pdf_compress_service.compress_pdf(
                file_path=request.file,
                compression_level=request.compression_level
            )
            
            # Update conversion record with results
            update_data = {
                "status": ConversionStatus.COMPLETED,
                "output_filename": result["output_file"],
                "file_size": result["compressed_size"]
            }
            
            conversion = crud_conversion.update(
                db=db, 
                db_obj=conversion, 
                obj_in=update_data
            )
            
            return conversion
            
        except Exception as e:
            # Update status to failed
            crud_conversion.update_status(
                db=db, 
                db_obj=conversion, 
                status=ConversionStatus.FAILED,
                error_message=str(e)
            )
            
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Compression operation failed: {str(e)}"
            )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error during compression operation: {str(e)}"
        )


@router.get("/preview/{file_path}")
async def get_compression_preview(file_path: str):
    """
    Get compression preview for all levels without actually compressing
    
    - **file_path**: Path to the PDF file
    
    Returns estimated compression results for all levels.
    """
    try:
        preview = await pdf_compress_service.get_compression_preview(file_path)
        return {
            "success": True,
            "file_path": file_path,
            "preview": preview
        }
    except FileNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"PDF file not found: {file_path}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating compression preview: {str(e)}"
        )


@router.get("/info/{file_path}")
async def get_pdf_info(file_path: str):
    """
    Get information about a PDF file
    
    - **file_path**: Path to the PDF file
    
    Returns PDF metadata including page count, title, author, etc.
    """
    try:
        info = await pdf_compress_service.get_pdf_info(file_path)
        return {
            "success": True,
            "file_path": file_path,
            "info": info
        }
    except FileNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"PDF file not found: {file_path}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error reading PDF info: {str(e)}"
        )
