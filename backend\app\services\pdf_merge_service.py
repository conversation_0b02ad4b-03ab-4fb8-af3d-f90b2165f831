import os
import async<PERSON>
from typing import List, Optional
from pathlib import Path
import PyPDF2
from PyPDF2 import PdfWriter, PdfReader
from app.core.config import settings
from app.utils.file_utils import validate_pdf_file, get_file_size
from app.utils.validation import validate_file_size


class PDFMergeService:
    """Service for merging PDF files"""
    
    def __init__(self):
        self.max_file_size = 100 * 1024 * 1024  # 100MB
        self.max_files = 50
        self.upload_dir = Path(settings.UPLOAD_DIR)
    
    async def merge_pdfs(
        self, 
        file_paths: List[str], 
        output_filename: Optional[str] = None
    ) -> dict:
        """
        Merge multiple PDF files into a single PDF
        
        Args:
            file_paths: List of PDF file paths to merge
            output_filename: Optional custom output filename
            
        Returns:
            dict: Result containing output file path and metadata
            
        Raises:
            ValueError: If validation fails
            FileNotFoundError: If input files don't exist
            Exception: If merge operation fails
        """
        try:
            # Validate inputs
            await self._validate_merge_inputs(file_paths)
            
            # Create output filename if not provided
            if not output_filename:
                output_filename = f"merged_{len(file_paths)}_files.pdf"
            elif not output_filename.endswith('.pdf'):
                output_filename += '.pdf'
            
            output_path = self.upload_dir / output_filename
            
            # Perform merge operation
            total_pages = await self._merge_pdf_files(file_paths, output_path)
            
            # Get output file info
            output_size = await get_file_size(str(output_path))
            
            return {
                "success": True,
                "output_file": str(output_path),
                "output_filename": output_filename,
                "total_pages": total_pages,
                "file_size": output_size,
                "input_files_count": len(file_paths)
            }
            
        except Exception as e:
            # Clean up output file if it exists
            if 'output_path' in locals() and output_path.exists():
                output_path.unlink()
            raise e
    
    async def _validate_merge_inputs(self, file_paths: List[str]) -> None:
        """Validate merge operation inputs"""
        
        # Check file count
        if len(file_paths) < 2:
            raise ValueError("At least 2 files are required for merging")
        
        if len(file_paths) > self.max_files:
            raise ValueError(f"Maximum {self.max_files} files allowed for merging")
        
        # Validate each file
        total_size = 0
        for file_path in file_paths:
            full_path = self.upload_dir / file_path
            
            # Check if file exists
            if not full_path.exists():
                raise FileNotFoundError(f"File not found: {file_path}")
            
            # Validate PDF format
            if not await validate_pdf_file(str(full_path)):
                raise ValueError(f"Invalid PDF file: {file_path}")
            
            # Check file size
            file_size = await get_file_size(str(full_path))
            if not validate_file_size(file_size, self.max_file_size):
                raise ValueError(f"File too large: {file_path} ({file_size} bytes)")
            
            total_size += file_size
        
        # Check total size
        if total_size > self.max_file_size * 2:  # Allow up to 200MB total
            raise ValueError(f"Total file size too large: {total_size} bytes")
    
    async def _merge_pdf_files(self, file_paths: List[str], output_path: Path) -> int:
        """Perform the actual PDF merge operation"""
        
        def merge_operation():
            writer = PdfWriter()
            total_pages = 0
            
            for file_path in file_paths:
                full_path = self.upload_dir / file_path
                
                try:
                    with open(full_path, 'rb') as file:
                        reader = PdfReader(file)
                        
                        # Add all pages from this PDF
                        for page in reader.pages:
                            writer.add_page(page)
                            total_pages += 1
                            
                except Exception as e:
                    raise Exception(f"Error reading PDF {file_path}: {str(e)}")
            
            # Write merged PDF
            with open(output_path, 'wb') as output_file:
                writer.write(output_file)
            
            return total_pages
        
        # Run merge operation in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, merge_operation)
    
    async def get_pdf_info(self, file_path: str) -> dict:
        """Get information about a PDF file"""
        
        def get_info():
            full_path = self.upload_dir / file_path
            
            with open(full_path, 'rb') as file:
                reader = PdfReader(file)
                
                return {
                    "pages": len(reader.pages),
                    "title": reader.metadata.get('/Title', '') if reader.metadata else '',
                    "author": reader.metadata.get('/Author', '') if reader.metadata else '',
                    "subject": reader.metadata.get('/Subject', '') if reader.metadata else '',
                    "creator": reader.metadata.get('/Creator', '') if reader.metadata else '',
                    "producer": reader.metadata.get('/Producer', '') if reader.metadata else '',
                }
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, get_info)


# Global service instance
pdf_merge_service = PDFMergeService()
