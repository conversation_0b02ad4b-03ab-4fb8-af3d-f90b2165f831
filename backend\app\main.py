import os
from contextlib import asynccontextmanager

from fastapi import FastAPI
from app.core.config import settings
from app.api.endpoints import files, health, conversion, merge, split

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup: Ensure upload directory exists
    upload_dir = settings.UPLOAD_DIR
    os.makedirs(upload_dir, exist_ok=True)
    yield
    # Shutdown: cleanup code would go here

app = FastAPI(title="My Backend", lifespan=lifespan)

app.include_router(health.router, prefix="/health", tags=["health"])
app.include_router(files.router, prefix="/files", tags=["files"])
app.include_router(conversion.router, prefix="/conversion", tags=["conversion"])
app.include_router(merge.router, prefix="/conversions/merge", tags=["pdf-operations"])
