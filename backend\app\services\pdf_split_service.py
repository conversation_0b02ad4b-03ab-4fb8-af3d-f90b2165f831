import os
import async<PERSON>
from typing import List, Optional, Dict, Any
from pathlib import Path
import PyPDF2
from PyPDF2 import Pdf<PERSON>rite<PERSON>, PdfReader
from app.core.config import settings
from app.utils.file_utils import validate_pdf_file, get_file_size
from app.utils.validation import validate_file_size, validate_page_ranges, validate_split_mode


class PDFSplitService:
    """Service for splitting PDF files"""
    
    def __init__(self):
        self.max_file_size = 100 * 1024 * 1024  # 100MB
        self.upload_dir = Path(settings.UPLOAD_DIR)
    
    async def split_pdf(
        self, 
        file_path: str,
        split_mode: str = "ranges",
        ranges: Optional[str] = None,
        fixed_range: Optional[int] = None,
        remove_pages: Optional[str] = None,
        merge_after: bool = False
    ) -> Dict[str, Any]:
        """
        Split a PDF file based on specified mode and parameters
        
        Args:
            file_path: Path to the PDF file to split
            split_mode: Split mode (ranges, fixed_range, remove_pages, filesize)
            ranges: Page ranges for 'ranges' mode (e.g., "1,5,10-14")
            fixed_range: Fixed range size for 'fixed_range' mode
            remove_pages: Pages to remove for 'remove_pages' mode
            merge_after: Whether to merge all ranges after splitting
            
        Returns:
            dict: Result containing output files and metadata
            
        Raises:
            ValueError: If validation fails
            FileNotFoundError: If input file doesn't exist
            Exception: If split operation fails
        """
        try:
            # Validate inputs
            await self._validate_split_inputs(file_path, split_mode, ranges, fixed_range, remove_pages)
            
            full_path = self.upload_dir / file_path
            
            # Get PDF info
            pdf_info = await self._get_pdf_info(full_path)
            total_pages = pdf_info["pages"]
            
            # Perform split operation based on mode
            if split_mode == "ranges":
                result = await self._split_by_ranges(full_path, ranges, total_pages, merge_after)
            elif split_mode == "fixed_range":
                result = await self._split_by_fixed_range(full_path, fixed_range, total_pages, merge_after)
            elif split_mode == "remove_pages":
                result = await self._split_by_removing_pages(full_path, remove_pages, total_pages)
            elif split_mode == "filesize":
                result = await self._split_by_filesize(full_path, total_pages)
            else:
                raise ValueError(f"Unsupported split mode: {split_mode}")
            
            return {
                "success": True,
                "split_mode": split_mode,
                "input_file": file_path,
                "total_input_pages": total_pages,
                **result
            }
            
        except Exception as e:
            # Clean up any created files on error
            if 'result' in locals() and 'output_files' in result:
                for output_file in result['output_files']:
                    try:
                        (self.upload_dir / output_file).unlink(missing_ok=True)
                    except:
                        pass
            raise e
    
    async def _validate_split_inputs(
        self, 
        file_path: str, 
        split_mode: str, 
        ranges: Optional[str], 
        fixed_range: Optional[int], 
        remove_pages: Optional[str]
    ) -> None:
        """Validate split operation inputs"""
        
        full_path = self.upload_dir / file_path
        
        # Check if file exists
        if not full_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        # Validate PDF format
        if not await validate_pdf_file(str(full_path)):
            raise ValueError(f"Invalid PDF file: {file_path}")
        
        # Check file size
        file_size = await get_file_size(str(full_path))
        if not validate_file_size(file_size, self.max_file_size):
            raise ValueError(f"File too large: {file_path} ({file_size} bytes)")
        
        # Validate split mode
        if not validate_split_mode(split_mode):
            raise ValueError(f"Invalid split mode: {split_mode}")
        
        # Validate mode-specific parameters
        if split_mode == "ranges" and not ranges:
            raise ValueError("Ranges parameter is required for 'ranges' split mode")
        
        if split_mode == "fixed_range" and not fixed_range:
            raise ValueError("Fixed_range parameter is required for 'fixed_range' split mode")
        
        if split_mode == "remove_pages" and not remove_pages:
            raise ValueError("Remove_pages parameter is required for 'remove_pages' split mode")
        
        if split_mode == "fixed_range" and fixed_range and fixed_range < 1:
            raise ValueError("Fixed_range must be at least 1")
    
    async def _get_pdf_info(self, file_path: Path) -> Dict[str, Any]:
        """Get PDF information"""
        
        def get_info():
            with open(file_path, 'rb') as file:
                reader = PdfReader(file)
                return {
                    "pages": len(reader.pages),
                    "title": reader.metadata.get('/Title', '') if reader.metadata else '',
                    "author": reader.metadata.get('/Author', '') if reader.metadata else ''
                }
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, get_info)
    
    async def _split_by_ranges(
        self, 
        file_path: Path, 
        ranges: str, 
        total_pages: int, 
        merge_after: bool
    ) -> Dict[str, Any]:
        """Split PDF by specified page ranges"""
        
        # Validate ranges
        if not validate_page_ranges(ranges, total_pages):
            raise ValueError(f"Invalid page ranges: {ranges}")
        
        def split_operation():
            output_files = []
            all_pages = []
            
            with open(file_path, 'rb') as file:
                reader = PdfReader(file)
                
                # Parse ranges
                range_parts = ranges.split(',')
                
                for i, range_part in enumerate(range_parts):
                    range_part = range_part.strip()
                    writer = PdfWriter()
                    
                    if '-' in range_part:
                        # Range format (e.g., "10-14")
                        start, end = range_part.split('-', 1)
                        start_page = int(start.strip()) - 1  # Convert to 0-based
                        end_page = int(end.strip()) - 1
                        
                        for page_num in range(start_page, end_page + 1):
                            if 0 <= page_num < len(reader.pages):
                                page = reader.pages[page_num]
                                writer.add_page(page)
                                if merge_after:
                                    all_pages.append(page)
                    else:
                        # Single page
                        page_num = int(range_part) - 1  # Convert to 0-based
                        if 0 <= page_num < len(reader.pages):
                            page = reader.pages[page_num]
                            writer.add_page(page)
                            if merge_after:
                                all_pages.append(page)
                    
                    # Save individual range file
                    if not merge_after:
                        output_filename = f"split_range_{i+1}_{file_path.stem}.pdf"
                        output_path = file_path.parent / output_filename
                        
                        with open(output_path, 'wb') as output_file:
                            writer.write(output_file)
                        
                        output_files.append(output_filename)
                
                # If merge_after is True, create a single merged file
                if merge_after and all_pages:
                    merged_writer = PdfWriter()
                    for page in all_pages:
                        merged_writer.add_page(page)
                    
                    output_filename = f"split_merged_{file_path.stem}.pdf"
                    output_path = file_path.parent / output_filename
                    
                    with open(output_path, 'wb') as output_file:
                        merged_writer.write(output_file)
                    
                    output_files = [output_filename]
            
            return output_files
        
        loop = asyncio.get_event_loop()
        output_files = await loop.run_in_executor(None, split_operation)
        
        # Get output file sizes
        output_info = []
        total_size = 0
        
        for filename in output_files:
            file_size = await get_file_size(str(self.upload_dir / filename))
            output_info.append({
                "filename": filename,
                "size": file_size
            })
            total_size += file_size
        
        return {
            "output_files": output_files,
            "output_count": len(output_files),
            "output_info": output_info,
            "total_output_size": total_size
        }
    
    async def _split_by_fixed_range(
        self, 
        file_path: Path, 
        fixed_range: int, 
        total_pages: int, 
        merge_after: bool
    ) -> Dict[str, Any]:
        """Split PDF by fixed page ranges"""
        
        def split_operation():
            output_files = []
            all_pages = []
            
            with open(file_path, 'rb') as file:
                reader = PdfReader(file)
                
                # Split into fixed ranges
                for start_page in range(0, total_pages, fixed_range):
                    end_page = min(start_page + fixed_range - 1, total_pages - 1)
                    
                    writer = PdfWriter()
                    
                    for page_num in range(start_page, end_page + 1):
                        page = reader.pages[page_num]
                        writer.add_page(page)
                        if merge_after:
                            all_pages.append(page)
                    
                    # Save individual range file
                    if not merge_after:
                        range_num = (start_page // fixed_range) + 1
                        output_filename = f"split_fixed_{range_num}_{file_path.stem}.pdf"
                        output_path = file_path.parent / output_filename
                        
                        with open(output_path, 'wb') as output_file:
                            writer.write(output_file)
                        
                        output_files.append(output_filename)
                
                # If merge_after is True, create a single merged file
                if merge_after and all_pages:
                    merged_writer = PdfWriter()
                    for page in all_pages:
                        merged_writer.add_page(page)
                    
                    output_filename = f"split_fixed_merged_{file_path.stem}.pdf"
                    output_path = file_path.parent / output_filename
                    
                    with open(output_path, 'wb') as output_file:
                        merged_writer.write(output_file)
                    
                    output_files = [output_filename]
            
            return output_files
        
        loop = asyncio.get_event_loop()
        output_files = await loop.run_in_executor(None, split_operation)
        
        # Get output file sizes
        output_info = []
        total_size = 0
        
        for filename in output_files:
            file_size = await get_file_size(str(self.upload_dir / filename))
            output_info.append({
                "filename": filename,
                "size": file_size
            })
            total_size += file_size
        
        return {
            "output_files": output_files,
            "output_count": len(output_files),
            "output_info": output_info,
            "total_output_size": total_size
        }
    
    async def _split_by_removing_pages(
        self, 
        file_path: Path, 
        remove_pages: str, 
        total_pages: int
    ) -> Dict[str, Any]:
        """Split PDF by removing specified pages"""
        
        # Validate remove_pages format
        if not validate_page_ranges(remove_pages, total_pages):
            raise ValueError(f"Invalid remove_pages format: {remove_pages}")
        
        def split_operation():
            # Parse pages to remove
            pages_to_remove = set()
            
            for range_part in remove_pages.split(','):
                range_part = range_part.strip()
                
                if '-' in range_part:
                    start, end = range_part.split('-', 1)
                    start_page = int(start.strip())
                    end_page = int(end.strip())
                    
                    for page_num in range(start_page, end_page + 1):
                        pages_to_remove.add(page_num - 1)  # Convert to 0-based
                else:
                    page_num = int(range_part) - 1  # Convert to 0-based
                    pages_to_remove.add(page_num)
            
            # Create new PDF without removed pages
            with open(file_path, 'rb') as file:
                reader = PdfReader(file)
                writer = PdfWriter()
                
                for page_num in range(len(reader.pages)):
                    if page_num not in pages_to_remove:
                        writer.add_page(reader.pages[page_num])
                
                output_filename = f"split_removed_pages_{file_path.stem}.pdf"
                output_path = file_path.parent / output_filename
                
                with open(output_path, 'wb') as output_file:
                    writer.write(output_file)
                
                return [output_filename]
        
        loop = asyncio.get_event_loop()
        output_files = await loop.run_in_executor(None, split_operation)
        
        # Get output file info
        output_info = []
        total_size = 0
        
        for filename in output_files:
            file_size = await get_file_size(str(self.upload_dir / filename))
            output_info.append({
                "filename": filename,
                "size": file_size
            })
            total_size += file_size
        
        return {
            "output_files": output_files,
            "output_count": len(output_files),
            "output_info": output_info,
            "total_output_size": total_size
        }
    
    async def _split_by_filesize(self, file_path: Path, total_pages: int) -> Dict[str, Any]:
        """Split PDF by file size (simplified implementation)"""
        # For now, implement a simple version that splits into chunks of 10 pages
        # This can be enhanced later with actual file size calculations
        
        return await self._split_by_fixed_range(file_path, 10, total_pages, False)


# Global service instance
pdf_split_service = PDFSplitService()
