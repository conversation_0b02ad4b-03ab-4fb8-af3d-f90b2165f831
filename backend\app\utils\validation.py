from pathlib import Path


def validate_file_size(file_size: int, max_size: int) -> bool:
    """
    Validate file size against maximum allowed size

    Args:
        file_size: File size in bytes
        max_size: Maximum allowed size in bytes

    Returns:
        bool: True if file size is valid
    """
    return 0 < file_size <= max_size


def validate_filename(filename: str) -> bool:
    """
    Validate filename for security and format

    Args:
        filename: Filename to validate

    Returns:
        bool: True if filename is valid
    """
    if not filename or len(filename) > 255:
        return False

    # Check for invalid characters
    invalid_chars = '<>:"/\\|?*'
    if any(char in filename for char in invalid_chars):
        return False

    # Check for reserved names (Windows)
    reserved_names = {
        'CON', 'PRN', 'AUX', 'NUL',
        'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
        'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
    }

    name_without_ext = Path(filename).stem.upper()
    if name_without_ext in reserved_names:
        return False

    return True


def validate_pdf_extension(filename: str) -> bool:
    """
    Validate that filename has PDF extension

    Args:
        filename: Filename to validate

    Returns:
        bool: True if has PDF extension
    """
    return filename.lower().endswith('.pdf')


def validate_image_extension(filename: str) -> bool:
    """
    Validate that filename has valid image extension

    Args:
        filename: Filename to validate

    Returns:
        bool: True if has valid image extension
    """
    valid_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
    extension = Path(filename).suffix.lower()
    return extension in valid_extensions


def validate_page_ranges(ranges: str, max_pages: int) -> bool:
    """
    Validate page ranges format and values

    Args:
        ranges: Page ranges string (e.g., "1,5,10-14")
        max_pages: Maximum number of pages in document

    Returns:
        bool: True if ranges are valid
    """
    if not ranges:
        return False

    try:
        # Split by comma and validate each range
        for range_part in ranges.split(','):
            range_part = range_part.strip()

            if '-' in range_part:
                # Range format (e.g., "10-14")
                start, end = range_part.split('-', 1)
                start_page = int(start.strip())
                end_page = int(end.strip())

                if start_page < 1 or end_page > max_pages or start_page > end_page:
                    return False
            else:
                # Single page
                page = int(range_part)
                if page < 1 or page > max_pages:
                    return False

        return True
    except (ValueError, AttributeError):
        return False


def validate_compression_level(level: str) -> bool:
    """
    Validate compression level

    Args:
        level: Compression level string

    Returns:
        bool: True if valid compression level
    """
    valid_levels = {'extreme', 'recommended', 'low'}
    return level.lower() in valid_levels


def validate_orientation(orientation: str) -> bool:
    """
    Validate page orientation

    Args:
        orientation: Orientation string

    Returns:
        bool: True if valid orientation
    """
    valid_orientations = {'portrait', 'landscape'}
    return orientation.lower() in valid_orientations


def validate_page_size(page_size: str) -> bool:
    """
    Validate page size

    Args:
        page_size: Page size string

    Returns:
        bool: True if valid page size
    """
    valid_sizes = {'fit', 'a4', 'letter', 'legal', 'a3', 'a5'}
    return page_size.lower() in valid_sizes


def validate_split_mode(mode: str) -> bool:
    """
    Validate split mode

    Args:
        mode: Split mode string

    Returns:
        bool: True if valid split mode
    """
    valid_modes = {'ranges', 'fixed_range', 'remove_pages', 'filesize'}
    return mode.lower() in valid_modes


def validate_output_format(format_type: str) -> bool:
    """
    Validate output format

    Args:
        format_type: Output format string

    Returns:
        bool: True if valid output format
    """
    valid_formats = {'pdf', 'docx', 'doc', 'jpg', 'jpeg', 'png'}
    return format_type.lower() in valid_formats


def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename by removing/replacing invalid characters

    Args:
        filename: Original filename

    Returns:
        str: Sanitized filename
    """
    # Remove invalid characters
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')

    # Remove leading/trailing spaces and dots
    filename = filename.strip(' .')

    # Ensure it's not empty
    if not filename:
        filename = 'file'

    # Limit length
    if len(filename) > 255:
        name, ext = Path(filename).stem, Path(filename).suffix
        filename = name[:255-len(ext)] + ext

    return filename


def validate_file_count(count: int, min_count: int = 1, max_count: int = 50) -> bool:
    """
    Validate file count is within allowed range

    Args:
        count: Number of files
        min_count: Minimum allowed files
        max_count: Maximum allowed files

    Returns:
        bool: True if count is valid
    """
    return min_count <= count <= max_count