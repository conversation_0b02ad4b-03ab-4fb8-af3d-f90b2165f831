
from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.crud import conversion as crud_conversion
from app.schemas.conversion import Conversion, ConversionCreate, ConversionUpdate
from app.models.conversion import ConversionStatus

router = APIRouter()

@router.post("/", response_model=Conversion)
def create_conversion(
    conversion_in: ConversionCreate,
    db: Session = Depends(get_db)
):
    """Create a new conversion job"""
    return crud_conversion.create(db=db, obj_in=conversion_in)

@router.get("/", response_model=List[Conversion])
def get_conversions(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """Get all conversions with pagination"""
    return crud_conversion.get_multi(db=db, skip=skip, limit=limit)

@router.get("/{conversion_id}", response_model=Conversion)
def get_conversion(
    conversion_id: int,
    db: Session = Depends(get_db)
):
    """Get a specific conversion by ID"""
    conversion = crud_conversion.get(db=db, id=conversion_id)
    if not conversion:
        raise HTTPException(status_code=404, detail="Conversion not found")
    return conversion

@router.put("/{conversion_id}", response_model=Conversion)
def update_conversion(
    conversion_id: int,
    conversion_update: ConversionUpdate,
    db: Session = Depends(get_db)
):
    """Update a conversion job"""
    conversion = crud_conversion.get(db=db, id=conversion_id)
    if not conversion:
        raise HTTPException(status_code=404, detail="Conversion not found")
    return crud_conversion.update(db=db, db_obj=conversion, obj_in=conversion_update)

@router.get("/status/{status}", response_model=List[Conversion])
def get_conversions_by_status(
    status: ConversionStatus,
    db: Session = Depends(get_db)
):
    """Get conversions by status"""
    return crud_conversion.get_by_status(db=db, status=status)

@router.post("/jpg-to-pdf", response_model=Conversion)
async def convert_jpg_to_pdf(
    input_filename: str,
    db: Session = Depends(get_db)
):
    """Convert JPG to PDF"""
    conversion_data = ConversionCreate(
        input_filename=input_filename,
        input_format="jpg",
        output_format="pdf"
    )
    conversion = crud_conversion.create(db=db, obj_in=conversion_data)
    # TODO: Add actual conversion logic here
    return conversion

@router.post("/pdf-to-jpg", response_model=Conversion)
async def convert_pdf_to_jpg(
    input_filename: str,
    db: Session = Depends(get_db)
):
    """Convert PDF to JPG"""
    conversion_data = ConversionCreate(
        input_filename=input_filename,
        input_format="pdf",
        output_format="jpg"
    )
    conversion = crud_conversion.create(db=db, obj_in=conversion_data)
    # TODO: Add actual conversion logic here
    return conversion

