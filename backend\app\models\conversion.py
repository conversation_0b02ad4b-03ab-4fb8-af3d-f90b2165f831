from sqlalchemy import Column, Integer, String, DateTime, Enum, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from app.core.database import Base


class ConversionStatus(str, enum.Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class TaskType(str, enum.Enum):
    JPG_TO_PDF = "jpg_to_pdf"
    PDF_TO_JPG = "pdf_to_jpg"
    MERGE = "merge"
    SPLIT = "split"
    COMPRESS = "compress"
    PDF_TO_WORD = "pdf_to_word"
    IMAGE_TO_PDF = "image_to_pdf"


# SQLAlchemy model for database
class ConversionRecord(Base):
    __tablename__ = "conversions"

    id = Column(Integer, primary_key=True, index=True)
    input_filename = Column(String, nullable=False)
    output_filename = Column(String, nullable=True)
    input_format = Column(String, nullable=False)
    output_format = Column(String, nullable=False)
    task_type = Column(Enum(TaskType), nullable=False)
    status = Column(Enum(ConversionStatus), default=ConversionStatus.PENDING, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    file_size = Column(Integer, nullable=True)
    error_message = Column(String, nullable=True)

    # Foreign key to files table (optional)
    input_file_id = Column(Integer, ForeignKey("files.id"), nullable=True)
    output_file_id = Column(Integer, ForeignKey("files.id"), nullable=True)

    # Relationships
    input_file = relationship("FileRecord", foreign_keys=[input_file_id])
    output_file = relationship("FileRecord", foreign_keys=[output_file_id])