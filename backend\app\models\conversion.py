from sqlalchemy import Column, Integer, String, DateTime, Enum, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

# SQLAlchemy Base for database models
Base = declarative_base()


class ConversionStatus(str, enum.Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


# SQLAlchemy model for database
class ConversionRecord(Base):
    __tablename__ = "conversions"

    id = Column(Integer, primary_key=True, index=True)
    input_filename = Column(String, nullable=False)
    output_filename = Column(String, nullable=True)
    input_format = Column(String, nullable=False)
    output_format = Column(String, nullable=False)
    status = Column(Enum(ConversionStatus), default=ConversionStatus.PENDING, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    file_size = Column(Integer, nullable=True)
    error_message = Column(String, nullable=True)

    # Foreign key to files table (optional)
    input_file_id = Column(Integer, ForeignKey("files.id"), nullable=True)
    output_file_id = Column(Integer, ForeignKey("files.id"), nullable=True)

    # Relationships
    input_file = relationship("FileRecord", foreign_keys=[input_file_id])
    output_file = relationship("FileRecord", foreign_keys=[output_file_id])