import os
import async<PERSON>
from typing import Dict, Any
from pathlib import Path
import PyPDF2
from PyPDF2 import PdfWriter, PdfReader
from app.core.config import settings
from app.utils.file_utils import validate_pdf_file, get_file_size
from app.utils.validation import validate_file_size, validate_compression_level


class PDFCompressService:
    """Service for compressing PDF files"""
    
    def __init__(self):
        self.max_file_size = 100 * 1024 * 1024  # 100MB
        self.upload_dir = Path(settings.UPLOAD_DIR)

        # Compression settings for different levels
        self.compression_settings = {
            "extreme": {
                "remove_duplication": True,
                "remove_images": False,
                "image_quality": 50,
                "compress_streams": True
            },
            "recommended": {
                "remove_duplication": True,
                "remove_images": False,
                "image_quality": 75,
                "compress_streams": True
            },
            "low": {
                "remove_duplication": False,
                "remove_images": False,
                "image_quality": 90,
                "compress_streams": False
            }
        }
    
    async def compress_pdf(
        self, 
        file_path: str,
        compression_level: str = "recommended"
    ) -> Dict[str, Any]:
        """
        Compress a PDF file with specified compression level
        
        Args:
            file_path: Path to the PDF file to compress
            compression_level: Compression level (extreme, recommended, low)
            
        Returns:
            dict: Result containing output file path and compression statistics
            
        Raises:
            ValueError: If validation fails
            FileNotFoundError: If input file doesn't exist
            Exception: If compression operation fails
        """
        try:
            # Validate inputs
            await self._validate_compress_inputs(file_path, compression_level)
            
            full_path = self.upload_dir / file_path
            
            # Get original file info
            original_size = await get_file_size(str(full_path))
            
            # Create output filename
            output_filename = f"compressed_{compression_level}_{Path(file_path).stem}.pdf"
            output_path = self.upload_dir / output_filename
            
            # Perform compression operation
            result = await self._compress_pdf_file(full_path, output_path, compression_level)
            
            # Get compressed file info
            compressed_size = await get_file_size(str(output_path))
            compression_ratio = ((original_size - compressed_size) / original_size) * 100 if original_size > 0 else 0
            
            return {
                "success": True,
                "input_file": file_path,
                "output_file": output_filename,
                "compression_level": compression_level,
                "original_size": original_size,
                "compressed_size": compressed_size,
                "compression_ratio": round(compression_ratio, 2),
                "size_reduction": original_size - compressed_size,
                **result
            }
            
        except Exception as e:
            # Clean up output file if it exists
            if 'output_path' in locals() and output_path.exists():
                output_path.unlink()
            raise e
    
    async def _validate_compress_inputs(self, file_path: str, compression_level: str) -> None:
        """Validate compression operation inputs"""
        
        full_path = self.upload_dir / file_path
        
        # Check if file exists
        if not full_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        # Validate PDF format
        if not await validate_pdf_file(str(full_path)):
            raise ValueError(f"Invalid PDF file: {file_path}")
        
        # Check file size
        file_size = await get_file_size(str(full_path))
        if not validate_file_size(file_size, self.max_file_size):
            raise ValueError(f"File too large: {file_path} ({file_size} bytes)")
        
        # Validate compression level
        if not validate_compression_level(compression_level):
            raise ValueError(f"Invalid compression level: {compression_level}")
    
    async def _compress_pdf_file(
        self, 
        input_path: Path, 
        output_path: Path, 
        compression_level: str
    ) -> Dict[str, Any]:
        """Perform the actual PDF compression operation"""
        
        def compress_operation():
            # Simple compression using PyPDF2
            with open(input_path, 'rb') as file:
                reader = PdfReader(file)
                writer = PdfWriter()

                # Copy all pages
                for page in reader.pages:
                    writer.add_page(page)

                # Apply compression based on level
                if compression_level in ["extreme", "recommended"]:
                    writer.compress_identical_objects()
                    writer.remove_duplication()

                # Write compressed PDF
                with open(output_path, 'wb') as output_file:
                    writer.write(output_file)

                return {
                    "pages_processed": len(reader.pages),
                    "metadata_preserved": bool(reader.metadata),
                    "compression_settings": compression_level
                }
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, compress_operation)
    
    async def get_compression_preview(self, file_path: str) -> Dict[str, Any]:
        """Get compression preview for all levels without actually compressing"""
        
        full_path = self.upload_dir / file_path
        
        if not full_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        original_size = await get_file_size(str(full_path))
        
        # Estimate compression ratios based on typical results
        estimated_ratios = {
            "extreme": 60,  # 60% reduction
            "recommended": 40,  # 40% reduction
            "low": 15  # 15% reduction
        }
        
        preview = {
            "original_size": original_size,
            "estimates": {}
        }
        
        for level, ratio in estimated_ratios.items():
            estimated_size = int(original_size * (1 - ratio / 100))
            preview["estimates"][level] = {
                "estimated_size": estimated_size,
                "estimated_reduction": original_size - estimated_size,
                "estimated_ratio": ratio
            }
        
        return preview
    
    async def get_pdf_info(self, file_path: str) -> Dict[str, Any]:
        """Get detailed information about a PDF file"""

        def get_info():
            full_path = self.upload_dir / file_path

            with open(full_path, 'rb') as file:
                reader = PdfReader(file)

                metadata = reader.metadata or {}
                page_count = len(reader.pages)

                return {
                    "pages": page_count,
                    "title": metadata.get('/Title', ''),
                    "author": metadata.get('/Author', ''),
                    "subject": metadata.get('/Subject', ''),
                    "creator": metadata.get('/Creator', ''),
                    "producer": metadata.get('/Producer', ''),
                    "creation_date": metadata.get('/CreationDate', ''),
                    "modification_date": metadata.get('/ModDate', ''),
                    "encrypted": reader.is_encrypted
                }

        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, get_info)


# Global service instance
pdf_compress_service = PDFCompressService()
