import os
import asyncio
from typing import Dict, Any
from pathlib import Path
import fitz  # PyMuPDF
from app.core.config import settings
from app.utils.file_utils import validate_pdf_file, get_file_size
from app.utils.validation import validate_file_size, validate_compression_level


class PDFCompressService:
    """Service for compressing PDF files"""
    
    def __init__(self):
        self.max_file_size = 100 * 1024 * 1024  # 100MB
        self.upload_dir = Path(settings.UPLOAD_DIR)
        
        # Compression settings for different levels
        self.compression_settings = {
            "extreme": {
                "deflate": True,
                "deflate_images": True,
                "deflate_fonts": True,
                "garbage": 4,
                "clean": True,
                "sanitize": True,
                "ascii": True,
                "linear": True,
                "pretty": False,
                "compress_images": True,
                "image_quality": 50  # Lower quality for extreme compression
            },
            "recommended": {
                "deflate": True,
                "deflate_images": True,
                "deflate_fonts": True,
                "garbage": 3,
                "clean": True,
                "sanitize": False,
                "ascii": False,
                "linear": True,
                "pretty": False,
                "compress_images": True,
                "image_quality": 75  # Balanced quality
            },
            "low": {
                "deflate": True,
                "deflate_images": False,
                "deflate_fonts": False,
                "garbage": 1,
                "clean": False,
                "sanitize": False,
                "ascii": False,
                "linear": False,
                "pretty": True,
                "compress_images": False,
                "image_quality": 90  # High quality, minimal compression
            }
        }
    
    async def compress_pdf(
        self, 
        file_path: str,
        compression_level: str = "recommended"
    ) -> Dict[str, Any]:
        """
        Compress a PDF file with specified compression level
        
        Args:
            file_path: Path to the PDF file to compress
            compression_level: Compression level (extreme, recommended, low)
            
        Returns:
            dict: Result containing output file path and compression statistics
            
        Raises:
            ValueError: If validation fails
            FileNotFoundError: If input file doesn't exist
            Exception: If compression operation fails
        """
        try:
            # Validate inputs
            await self._validate_compress_inputs(file_path, compression_level)
            
            full_path = self.upload_dir / file_path
            
            # Get original file info
            original_size = await get_file_size(str(full_path))
            
            # Create output filename
            output_filename = f"compressed_{compression_level}_{Path(file_path).stem}.pdf"
            output_path = self.upload_dir / output_filename
            
            # Perform compression operation
            result = await self._compress_pdf_file(full_path, output_path, compression_level)
            
            # Get compressed file info
            compressed_size = await get_file_size(str(output_path))
            compression_ratio = ((original_size - compressed_size) / original_size) * 100 if original_size > 0 else 0
            
            return {
                "success": True,
                "input_file": file_path,
                "output_file": output_filename,
                "compression_level": compression_level,
                "original_size": original_size,
                "compressed_size": compressed_size,
                "compression_ratio": round(compression_ratio, 2),
                "size_reduction": original_size - compressed_size,
                **result
            }
            
        except Exception as e:
            # Clean up output file if it exists
            if 'output_path' in locals() and output_path.exists():
                output_path.unlink()
            raise e
    
    async def _validate_compress_inputs(self, file_path: str, compression_level: str) -> None:
        """Validate compression operation inputs"""
        
        full_path = self.upload_dir / file_path
        
        # Check if file exists
        if not full_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        # Validate PDF format
        if not await validate_pdf_file(str(full_path)):
            raise ValueError(f"Invalid PDF file: {file_path}")
        
        # Check file size
        file_size = await get_file_size(str(full_path))
        if not validate_file_size(file_size, self.max_file_size):
            raise ValueError(f"File too large: {file_path} ({file_size} bytes)")
        
        # Validate compression level
        if not validate_compression_level(compression_level):
            raise ValueError(f"Invalid compression level: {compression_level}")
    
    async def _compress_pdf_file(
        self, 
        input_path: Path, 
        output_path: Path, 
        compression_level: str
    ) -> Dict[str, Any]:
        """Perform the actual PDF compression operation"""
        
        def compress_operation():
            # Get compression settings
            settings = self.compression_settings[compression_level]
            
            # Open the PDF document
            doc = fitz.open(str(input_path))
            
            try:
                # Get document info
                page_count = doc.page_count
                metadata = doc.metadata
                
                # Compress images if specified
                if settings.get("compress_images", False):
                    image_quality = settings.get("image_quality", 75)
                    
                    for page_num in range(page_count):
                        page = doc[page_num]
                        
                        # Get images on the page
                        image_list = page.get_images()
                        
                        for img_index, img in enumerate(image_list):
                            try:
                                # Extract image
                                xref = img[0]
                                base_image = doc.extract_image(xref)
                                image_bytes = base_image["image"]
                                image_ext = base_image["ext"]
                                
                                # Compress image if it's JPEG or PNG
                                if image_ext in ["jpeg", "jpg", "png"]:
                                    # Create a new pixmap with reduced quality
                                    pix = fitz.Pixmap(image_bytes)
                                    
                                    # Convert to JPEG with specified quality
                                    if pix.n - pix.alpha < 4:  # GRAY or RGB
                                        compressed_bytes = pix.tobytes("jpeg", jpg_quality=image_quality)
                                        
                                        # Replace the image in the document
                                        doc._replace_image(xref, compressed_bytes)
                                    
                                    pix = None  # Free memory
                                    
                            except Exception:
                                # Skip problematic images
                                continue
                
                # Save with compression options
                save_options = {
                    "garbage": settings.get("garbage", 3),
                    "clean": settings.get("clean", True),
                    "deflate": settings.get("deflate", True),
                    "deflate_images": settings.get("deflate_images", True),
                    "deflate_fonts": settings.get("deflate_fonts", True),
                    "sanitize": settings.get("sanitize", False),
                    "ascii": settings.get("ascii", False),
                    "linear": settings.get("linear", True),
                    "pretty": settings.get("pretty", False)
                }
                
                # Save the compressed PDF
                doc.save(str(output_path), **save_options)
                
                return {
                    "pages_processed": page_count,
                    "metadata_preserved": bool(metadata),
                    "compression_settings": compression_level
                }
                
            finally:
                doc.close()
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, compress_operation)
    
    async def get_compression_preview(self, file_path: str) -> Dict[str, Any]:
        """Get compression preview for all levels without actually compressing"""
        
        full_path = self.upload_dir / file_path
        
        if not full_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        original_size = await get_file_size(str(full_path))
        
        # Estimate compression ratios based on typical results
        estimated_ratios = {
            "extreme": 60,  # 60% reduction
            "recommended": 40,  # 40% reduction
            "low": 15  # 15% reduction
        }
        
        preview = {
            "original_size": original_size,
            "estimates": {}
        }
        
        for level, ratio in estimated_ratios.items():
            estimated_size = int(original_size * (1 - ratio / 100))
            preview["estimates"][level] = {
                "estimated_size": estimated_size,
                "estimated_reduction": original_size - estimated_size,
                "estimated_ratio": ratio
            }
        
        return preview
    
    async def get_pdf_info(self, file_path: str) -> Dict[str, Any]:
        """Get detailed information about a PDF file"""
        
        def get_info():
            full_path = self.upload_dir / file_path
            
            doc = fitz.open(str(full_path))
            
            try:
                metadata = doc.metadata
                page_count = doc.page_count
                
                # Count images
                total_images = 0
                for page_num in range(page_count):
                    page = doc[page_num]
                    total_images += len(page.get_images())
                
                return {
                    "pages": page_count,
                    "images": total_images,
                    "title": metadata.get("title", ""),
                    "author": metadata.get("author", ""),
                    "subject": metadata.get("subject", ""),
                    "creator": metadata.get("creator", ""),
                    "producer": metadata.get("producer", ""),
                    "creation_date": metadata.get("creationDate", ""),
                    "modification_date": metadata.get("modDate", ""),
                    "encrypted": doc.needs_pass,
                    "permissions": doc.permissions if hasattr(doc, 'permissions') else None
                }
                
            finally:
                doc.close()
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, get_info)


# Global service instance
pdf_compress_service = PDFCompressService()
