# Any2PDF Development Guidelines

**Type**: Always
**Description**: Core development guidelines for the Any2PDF project - a FastAPI backend that replicates iLovePDF.com functionality

## Project Overview
- **Project Name**: Any2PDF
- **Purpose**: Convert any format to PDF and PDF to any format, competing with ilovepdf.com
- **Architecture**: FastAPI backend + Angular frontend (focus on backend only)
- **Target Features**: PDF merge, split, compress, PDF-to-Word, image-to-PDF

## Development Environment Setup
- **Python Version**: 3.12
- **Virtual Environment**: Always activate venv first
- **Working Directory**: `backend/app/`
- **Development Server**: `fastapi dev main.py --reload`
- **Full Command Sequence**:
  1. Activate virtual environment
  2. `cd backend/app`
  3. `fastapi dev main.py --reload`

## Backend Architecture (FastAPI)

### Directory Structure
```
backend/app/
├── api/endpoints/          # API route handlers
├── core/                   # Core configuration and database
├── crud/                   # Database operations
├── models/                 # SQLAlchemy models
├── schemas/                # Pydantic schemas
├── services/               # Business logic services
└── utils/                  # Utility functions
```

### API Endpoint Patterns
- **Base URL Structure**: `/conversions/{operation}`
- **Target Endpoints**:
  - `POST /conversions/merge` - Merge PDFs
  - `POST /conversions/split` - Split PDF
  - `POST /conversions/compress` - Compress PDF
  - `POST /conversions/pdf-to-word` - PDF → Word
  - `POST /conversions/image-to-pdf` - Images → PDF

### File Handling Standards
- **Max File Size**: 100MB per file
- **Max Files**: 50 files for batch operations
- **Upload Directory**: `uploads/` (configurable via settings)
- **File Validation**: Always validate PDF format and file size
- **Async Operations**: All file processing must be async
- **Cleanup**: Always clean up temporary files on errors

### Database Models
- **Base**: Use unified `Base` from `app.core.database`
- **Conversion Tracking**: Use `ConversionRecord` with `TaskType` enum
- **Status Management**: Track PENDING → PROCESSING → COMPLETED/FAILED
- **Migrations**: Use Alembic for database schema changes

### Error Handling
- **HTTP Status Codes**:
  - 400: Bad Request (validation errors)
  - 404: File not found
  - 413: File too large
  - 500: Internal server error
- **Error Response Format**: `{"error": "ERROR_CODE", "detail": "description"}`
- **Cleanup**: Always clean up files on operation failure

## Code Quality Standards

### Imports and Dependencies
- **PDF Processing**: Use PyPDF2 for basic operations, PyMuPDF (fitz) for advanced features
- **Image Processing**: Use Pillow (PIL)
- **Async**: Use asyncio.get_event_loop().run_in_executor() for CPU-bound operations
- **Validation**: Create reusable validation functions in `utils/validation.py`

### Service Layer Pattern
- **Location**: `services/` directory
- **Naming**: `{operation}_service.py` (e.g., `pdf_merge_service.py`)
- **Structure**: Class-based services with async methods
- **Global Instance**: Provide global service instance at module level

### Schema Design
- **Base Schemas**: Extend from common base classes
- **Request Schemas**: Include validation with Field() constraints
- **Response Schemas**: Include example data in Config
- **Task-Specific**: Create specific request schemas for each operation

### Authentication
- **Current State**: No authentication required (all endpoints public)
- **Future**: JWT-based authentication planned
- **Security**: Basic file validation and size limits only

## Testing Requirements
- **Framework**: pytest with pytest-asyncio
- **Coverage**: Aim for 90%+ test coverage
- **Test Types**:
  - Unit tests for services
  - Integration tests for endpoints
  - File processing validation tests
- **Test Data**: Use small sample PDF files for testing

## Documentation Standards
- **API Docs**: Auto-generated via FastAPI/OpenAPI
- **Code Comments**: Document complex PDF operations
- **README Updates**: Keep setup instructions current
- **Endpoint Documentation**: Include request/response examples

## Performance Guidelines
- **Async Processing**: Never block the event loop with file operations
- **Memory Management**: Process large files in chunks when possible
- **File Cleanup**: Implement proper temporary file cleanup
- **Error Recovery**: Graceful handling of corrupted or invalid files

## Deployment Considerations
- **Environment**: Docker containers for production
- **File Storage**: Local filesystem for development, cloud storage for production
- **Database**: SQLite for development, PostgreSQL for production
- **Monitoring**: Log all conversion operations and errors

## Forbidden Actions
- ❌ No frontend code modifications
- ❌ No authentication implementation (keep endpoints public)
- ❌ No sync I/O operations in services
- ❌ No manual file system operations (use utility functions)
- ❌ No hardcoded file paths (use settings)

## iLovePDF.com Compatibility
- **API Structure**: Mirror iLovePDF's request/response patterns
- **File Limits**: Match iLovePDF's constraints (100MB, 50 files)
- **Error Codes**: Use similar error messaging patterns
- **Feature Parity**: Implement core features with same functionality
