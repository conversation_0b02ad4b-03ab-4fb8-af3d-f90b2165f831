# backend/app/services/file_service.py
import shutil
from pathlib import Path
from fastapi import UploadFile
from app.core.config import settings

class FileService:
    async def save_file(self, upload_file: UploadFile) -> Path:
        """
        Save an UploadFile to the configured UPLOAD_DIR,
        preserving the original filename.
        """
        upload_dir: Path = settings.UPLOAD_DIR
        destination = upload_dir / upload_file.filename

        # Ensure no directory traversal attack
        if not destination.resolve().parent == upload_dir.resolve():
            raise ValueError("Invalid filename or path")

        # Stream file to disk
        with destination.open("wb") as buffer:
            shutil.copyfileobj(upload_file.file, buffer)

        # Close underlying SpooledTemporaryFile
        await upload_file.close()
        return destination
