"""Create conversions table with task_type

Revision ID: deb5d0b629e7
Revises: cd5476a75f92
Create Date: 2025-08-06 17:16:47.482562

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'deb5d0b629e7'
down_revision: Union[str, Sequence[str], None] = 'cd5476a75f92'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('users',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('email', sa.String(), nullable=False),
    sa.Column('username', sa.String(), nullable=True),
    sa.Column('hashed_password', sa.String(), nullable=False),
    sa.Column('full_name', sa.String(), nullable=True),
    sa.Column('is_active', sa.<PERSON>an(), nullable=True),
    sa.Column('is_superuser', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('last_login', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_table('conversions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('input_filename', sa.String(), nullable=False),
    sa.Column('output_filename', sa.String(), nullable=True),
    sa.Column('input_format', sa.String(), nullable=False),
    sa.Column('output_format', sa.String(), nullable=False),
    sa.Column('task_type', sa.Enum('JPG_TO_PDF', 'PDF_TO_JPG', 'MERGE', 'SPLIT', 'COMPRESS', 'PDF_TO_WORD', 'IMAGE_TO_PDF', name='tasktype'), nullable=False),
    sa.Column('status', sa.Enum('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', name='conversionstatus'), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('file_size', sa.Integer(), nullable=True),
    sa.Column('error_message', sa.String(), nullable=True),
    sa.Column('input_file_id', sa.Integer(), nullable=True),
    sa.Column('output_file_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['input_file_id'], ['files.id'], ),
    sa.ForeignKeyConstraint(['output_file_id'], ['files.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_conversions_id'), 'conversions', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_conversions_id'), table_name='conversions')
    op.drop_table('conversions')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    # ### end Alembic commands ###
